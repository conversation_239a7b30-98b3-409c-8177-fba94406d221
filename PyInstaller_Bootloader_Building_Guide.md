# Hướng dẫn Xây dựng Bootloader PyInstaller

## Tổng quan

PyInstaller đi kèm với các bootloader đã được biên dịch sẵn cho một số nền tảng trong thư mục `bootloader`. <PERSON>hi không có bootloader được biên dịch sẵn cho nền tảng hiện tại (hệ điều hành và kiến trúc), quá trình cài đặt pip sẽ cố gắng xây dựng một bootloader mới.

## Bootloader là gì?

Bootloader là thành phần quan trọng của PyInstaller, đóng vai trò như một executable nhỏ chịu trách nhiệm:
- Khởi động ứng dụng Python đã được đóng gói
- Quản lý việc giải nén và tải các module Python
- C<PERSON> cấp môi trường runtime cho ứng dụng

## Khi nào cần xây dựng Bootloader?

1. **Không có bootloader sẵn có**: <PERSON><PERSON> nền tảng của bạn không được hỗ trợ sẵn
2. **Tùy chỉnh bootloader**: Khi muốn sửa đổi mã nguồn bootloader
3. **Kiến trúc đặc biệt**: Khi cần bootloader cho kiến trúc cụ thể (32-bit, 64-bit)

## Quy trình Xây dựng Cơ bản

### Bước chuẩn bị:
1. Tải và cài đặt Python (cần thiết để chạy **waf**)
2. Clone hoặc tải mã nguồn từ [GitHub repository](https://github.com/pyinstaller/pyinstaller/)
3. Chuyển đến thư mục mã nguồn: `cd bootloader`
4. Xây dựng bootloader: `python ./waf all`

### Kết quả:
Quá trình này sẽ tạo ra các file bootloader:
- `../PyInstaller/bootloader/_OS_ARCH_/run`
- `../PyInstaller/bootloader/_OS_ARCH_/run_d` (debug version)
- `../PyInstaller/bootloader/_OS_ARCH_/runw` (chỉ macOS và Windows)
- `../PyInstaller/bootloader/_OS_ARCH_/runw_d` (debug version cho macOS và Windows)

## Xây dựng cho các Nền tảng Cụ thể

### GNU/Linux

**Yêu cầu công cụ phát triển:**
```bash
# Debian/Ubuntu
sudo apt-get install build-essential zlib1g-dev

# Fedora/RedHat
sudo yum groupinstall "Development Tools"
sudo yum install zlib-devel
```

### macOS

**Yêu cầu:**
- Xcode hoặc Command Line Tools for Xcode
- Clang compiler

**Tính năng Universal2:**
- Bootloader 64-bit mặc định được xây dựng như universal2 fat binaries (hỗ trợ cả x86_64 và arm64)
- Yêu cầu Xcode 12.2 trở lên
- Có thể điều khiển bằng flags `--universal2` hoặc `--no-universal2`

**Ví dụ xây dựng:**
```bash
# Universal2 (mặc định)
python waf all

# Thin native executable
python waf --no-universal2 all

# Thin x86_64 executable
CC='clang -arch x86_64' python waf --no-universal2 all

# Thin arm64 executable  
CC='clang -arch arm64' python waf --no-universal2 all
```

### Windows

**Ba tùy chọn chính:**

1. **Visual Studio C++** (Khuyến nghị)
   - Tạo executable tự chứa, tĩnh
   - Tương thích với mọi phiên bản Python
   - Yêu cầu Visual Studio 2015 trở lên

2. **MinGW-w64**
   - Tạo executable nhỏ hơn, liên kết động
   - Bị ràng buộc với phiên bản Python cụ thể
   - Phụ thuộc vào msvcrt.dll

3. **Cygwin và MinGW**
   - Tạo executable cho cygwin, không phải Windows thuần

**Xây dựng với Visual Studio:**
```bash
# Cài đặt qua chocolatey (khuyến nghị)
choco install -y python3 visualstudio2019-workload-vctools

# Xây dựng
python ./waf all
```

**Xây dựng với MinGW-w64:**
```bash
set PATH=C:\MinGW\bin;%PATH%
python ./waf --gcc all
```

### AIX

**Đặc điểm:**
- Mặc định xây dựng executable 32-bit
- Sử dụng biến môi trường `OBJECT_MODE` cho 64-bit

**Kiểm tra Python architecture:**
```bash
python -c "import sys; print(sys.maxsize <= 2**32)"
```

**Xây dựng:**
```bash
# 32-bit
unset OBJECT_MODE
python ./waf all

# 64-bit
export OBJECT_MODE=64
python ./waf all
```

### FreeBSD

**Hai cách tiếp cận:**
1. **Native build**: Trên máy FreeBSD thực
2. **Cross-compile**: Từ Linux sử dụng Docker

**Cross-compile với Docker:**
```bash
docker run -v $(pwd):/io -it freebsd-cross-build bash -c "cd /io/bootloader; ./waf all"
```

## Tùy chọn Nâng cao

### Kiến trúc Target
```bash
# Xây dựng bootloader 32-bit trên máy 64-bit
python ./waf all --target-arch=32bit
```

### Biến môi trường
- `PYINSTALLER_COMPILE_BOOTLOADER`: Buộc xây dựng bootloader ngay cả khi đã tồn tại
- `PYINSTALLER_BOOTLOADER_WAF_ARGS`: Truyền thêm tham số cho quá trình build

### Cross-building cho macOS
PyInstaller hỗ trợ cross-building cho macOS từ Linux sử dụng:
- Clang/LLVM compiler
- cctools (ld, lipo, ...)
- macOS SDK

## Virtual Machines với Vagrant

PyInstaller cung cấp các máy ảo để test và cross-build:

**Các guest có sẵn:**
- `linux64`: Xây dựng bootloader GNU/Linux
- `windows10`: Xây dựng bootloader Windows với Visual C++
- `build-osxcross`: Xây dựng SDK và cctools cho macOS

**Sử dụng:**
```bash
# Cài đặt plugins
vagrant plugin install vagrant-reload vagrant-scp

# Xây dựng
vagrant up linux64
vagrant halt linux64

# Cross-build cho macOS
export TARGET=OSX
vagrant up linux64
```

## Nền tảng được Hỗ trợ

**Chính thức:**
- GNU/Linux (gcc)
- Windows (Visual C++ VS2015+ hoặc MinGW gcc)
- macOS (clang)

**Đóng góp:**
- AIX (gcc hoặc xlc)
- HP-UX (gcc hoặc xlc)
- Solaris

## Lưu ý Quan trọng

1. **Tương thích compiler**: Bootloader phải được xây dựng với compiler tương thích với Python
2. **Kiến trúc phù hợp**: Bootloader phải khớp với kiến trúc của Python (32-bit/64-bit)
3. **Phụ thuộc runtime**: Một số tùy chọn tạo ra phụ thuộc vào thư viện runtime cụ thể
4. **Testing**: Luôn test bootloader sau khi xây dựng bằng cách chạy test suite

## Kết luận

Việc xây dựng bootloader PyInstaller là một quá trình phức tạp nhưng được tài liệu hóa tốt. Lựa chọn phương pháp xây dựng phù hợp phụ thuộc vào nền tảng target và yêu cầu cụ thể của dự án. Đối với hầu hết người dùng, bootloader có sẵn đã đủ, nhưng việc hiểu cách xây dựng custom bootloader mở ra nhiều khả năng tùy chỉnh nâng cao.
